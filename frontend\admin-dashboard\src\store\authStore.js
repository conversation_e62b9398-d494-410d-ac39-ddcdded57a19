import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { AuthService } from '../services/AuthService';

export const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      permissions: [],
      lastActivity: null,
      sessionTimeout: 30 * 60 * 1000, // 30 minutes

      // Actions
      login: async (credentials) => {
        set({ isLoading: true, error: null });
        try {
          const response = await AuthService.login(credentials);
          const { user, token, refreshToken, permissions } = response.data;
          
          set({
            user,
            token,
            refreshToken,
            permissions,
            isAuthenticated: true,
            isLoading: false,
            lastActivity: Date.now(),
            error: null
          });

          // Set up auto-refresh
          get().setupTokenRefresh();
          
          return { success: true, user };
        } catch (error) {
          set({
            isLoading: false,
            error: error.response?.data?.message || 'Lo<PERSON> failed'
          });
          return { success: false, error: error.message };
        }
      },

      logout: async () => {
        set({ isLoading: true });
        try {
          await AuthService.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
            permissions: [],
            lastActivity: null
          });
          
          // Clear refresh timer
          if (get().refreshTimer) {
            clearTimeout(get().refreshTimer);
          }
        }
      },

      refreshToken: async () => {
        const { refreshToken } = get();
        if (!refreshToken) return false;

        try {
          const response = await AuthService.refreshToken(refreshToken);
          const { token: newToken, refreshToken: newRefreshToken } = response.data;
          
          set({
            token: newToken,
            refreshToken: newRefreshToken,
            lastActivity: Date.now()
          });

          return true;
        } catch (error) {
          console.error('Token refresh failed:', error);
          get().logout();
          return false;
        }
      },

      setupTokenRefresh: () => {
        const { token, sessionTimeout } = get();
        if (!token) return;

        // Clear existing timer
        if (get().refreshTimer) {
          clearTimeout(get().refreshTimer);
        }

        // Set up new refresh timer (refresh 5 minutes before expiry)
        const refreshTime = sessionTimeout - (5 * 60 * 1000);
        const refreshTimer = setTimeout(() => {
          get().refreshToken();
        }, refreshTime);

        set({ refreshTimer });
      },

      updateLastActivity: () => {
        set({ lastActivity: Date.now() });
      },

      checkSession: () => {
        const { lastActivity, sessionTimeout, isAuthenticated } = get();
        if (!isAuthenticated || !lastActivity) return true;

        const now = Date.now();
        const timeSinceLastActivity = now - lastActivity;

        if (timeSinceLastActivity > sessionTimeout) {
          get().logout();
          return false;
        }

        return true;
      },

      initializeAuth: async () => {
        const { token, refreshToken } = get();
        
        if (!token || !refreshToken) {
          set({ isAuthenticated: false });
          return;
        }

        try {
          // Verify token validity
          const response = await AuthService.verifyToken(token);
          const { user, permissions } = response.data;
          
          set({
            user,
            permissions,
            isAuthenticated: true,
            lastActivity: Date.now()
          });

          get().setupTokenRefresh();
        } catch (error) {
          console.error('Token verification failed:', error);
          // Try to refresh token
          const refreshSuccess = await get().refreshToken();
          if (!refreshSuccess) {
            get().logout();
          }
        }
      },

      updateProfile: async (profileData) => {
        set({ isLoading: true, error: null });
        try {
          const response = await AuthService.updateProfile(profileData);
          const { user } = response.data;
          
          set({
            user: { ...get().user, ...user },
            isLoading: false
          });

          return { success: true, user };
        } catch (error) {
          set({
            isLoading: false,
            error: error.response?.data?.message || 'Profile update failed'
          });
          return { success: false, error: error.message };
        }
      },

      changePassword: async (passwordData) => {
        set({ isLoading: true, error: null });
        try {
          await AuthService.changePassword(passwordData);
          set({ isLoading: false });
          return { success: true };
        } catch (error) {
          set({
            isLoading: false,
            error: error.response?.data?.message || 'Password change failed'
          });
          return { success: false, error: error.message };
        }
      },

      hasPermission: (permission) => {
        const { permissions } = get();
        return permissions.includes(permission) || permissions.includes('admin');
      },

      hasRole: (role) => {
        const { user } = get();
        return user?.roles?.includes(role) || user?.role === role;
      },

      clearError: () => set({ error: null }),

      // Timer reference (not persisted)
      refreshTimer: null,
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
        permissions: state.permissions,
        lastActivity: state.lastActivity,
      }),
    }
  )
);
